var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();

Page({
  data: {
    type: 'file', // 默认为文件上传
    uploadType: '', // 上传方式
    index:0,
    uploadMethods: [
      {
        name: '微信聊天文件',
        icon: '/images/icon/icon_wechat.png',
        type: 'wechat'
      },
      {
        name: '本机文件',
        icon: '/images/icon/icon_sys_file.png',
        type: 'local',
        desc: '最大500M文件'
      },
      {
        name: '图片上传',
        icon: '/images/icon/icon_image_upload.png',
        type: 'image'
      }
    ],
    supportTypes: '支持Word/PDF/PPT/WPS/图片'
  },

  onLoad: function(options) {
    if (options.type) {
      this.setData({
        type: options.type
      });
    }
  },

  // 选择上传方式
  selectUploadType: function(e) {
    const type = e.currentTarget.dataset.type;
    
    // 根据不同的上传方式处理
    if (type === 'wechat') {
      // 微信聊天文件
      this.chooseMessageFile();
    } else if (type === 'local' || type === 'computer') {
      // 本地文件
      this.chooseLocalFile();
    } else if (type === 'image') {
      // 图片上传
      this.chooseImage();
    } else {
      // 其他上传方式
      wx.showToast({
        title: '该功能暂未开放',
        icon: 'none'
      });
    }
  },
  
  // 选择微信聊天文件
  chooseMessageFile: function() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      success: (res) => {
        const tempFile = res.tempFiles[0];
        this.uploadFile(tempFile);
      }
    });
  },
  
  // 选择本地文件
  chooseLocalFile: function() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      success: (res) => {
        const tempFile = res.tempFiles[0];
        this.uploadFile(tempFile);
      }
    });
  },
  generateRandomSixDigits() {
    return Math.floor(100000 + Math.random() * 900000);
  },
  // 选择图片
  chooseImage: function() {
    var that = this
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        console.log(res)
        const tempFilePath = res.tempFilePaths[0];
        const tempFile = {
          path: tempFilePath,
          name: '图片'+that.generateRandomSixDigits()
        };
        this.uploadFile(tempFile);
      }
    });
  },
  
  
  // 上传文件
  uploadFile: function(file) {
    let token = '';
    try {
      token = wx.getStorageSync('AccessToken').access_token;
    } catch (e) {
      console.log('获取Token失败');
    }
    console.log(file)
    wx.uploadFile({
      url: api.api_file_upload, //仅为示例，非真实的接口地址
      filePath: file.path,
      name: 'file',
      formData:{
        fileName: file.name
      },
      header: {
        'Authorization': token ? token : ''
      },
      success: (res) => {
        wx.navigateBack({
          delta: 1
        })
      }
    })
  }
}) 