
/* 轮播图区域 - 占满屏幕宽度 */
.swiper-container {
  margin: 25rpx 0;
}

.banner-swiper {
  height: 150px;
}

.banner-item {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 广告横幅 - 占满屏幕宽度 */
.slogan-banner {
  padding: 10rpx;
  background-color: #ffffff;
  color: #999;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
}


.title-row {
  display: flex;
  height: 40px;
  align-items: center;
}

.title-black {
  font-size: 38px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.title-orange {
  font-size: 38px;
  font-weight: bold;
  color: #FF6633;
  line-height: 1;
}

/* 功能快捷入口 - 占满屏幕宽度 */
.function-menu {
  background-color: #ffffff;
  padding: 15px 0;
  display: flex;
  border-bottom: 1px solid #f5f5f5;
}

.menu-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 10rpx;
}

.menu-item text {
  font-size: 14px;
  color: #666;
  text-align: center;
  white-space: nowrap;
}

/* 核心服务模块 */
.service-grid {
  margin: 25rpx 0;
}

.service-row {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
}

.service-divider {
  width: 3%;
}

.service-cell {
  width: 48.5%;
  border-radius: 10px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.service-cell.orange {
  background-color: #FF9966;
}

.service-cell.green {
  background-color: #66CCAA;
}

.service-content {
  margin-bottom: 10px;
}

.service-title {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 5px;
}

.service-desc {
  font-size: 14px;
  color: #ffffff;
  margin-bottom: 3px;
}

.service-support {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.service-button button {
  background-color: #ffffff;
  color: #333;
  font-size: 14px;
  border-radius: 20px;
  padding: 4px 15px;
  border: none;
  width: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-icon {
  width: 20px;
  height: 20px;
  margin-right: 4px;
}