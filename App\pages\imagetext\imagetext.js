var api = require('../../utils/api.js')
var sender = require('../../utils/sender.js')
const app = getApp();

Page({
  data: {
    imagetextId: '', // 图文ID
    imagetextDetail: {} // 图文详情
  },

  onLoad: function(options) {
    // 设置状态栏高度
    this.setData({
      statusBarHeight: app.globalData.statusBarHeight || 20,
      imagetextId: options.id || ''
    });

    if (this.data.imagetextId) {
      // 获取图文详情
      this.getImagetextDetail();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 获取图文详情
  getImagetextDetail: function() {
    let that = this;

    sender.requestUrl({
      url: api.api_imagetext_detail,
      method: 'GET',
      params: { id: that.data.imagetextId }
    }, function(data) {
      // 处理获取到的数据
      if (data) {
        that.setData({
          imagetextDetail: data
        });
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: data.title || '详情'
        });
      } else {
        wx.showToast({
          title: '获取详情失败',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  }
}) 