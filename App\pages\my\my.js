const app = getApp();
var api = require('../../utils/api.js');
var sender = require('../../utils/sender.js');

Page({
  data: {
    userInfo: {
      phone: '176****2521' // 隐藏部分手机号保护隐私
    },
    statusBarHeight: 20,
    navBarHeight: 44,
    navTotalHeight: 64
  },
  
  onLoad: function(options) {
    // 设置状态栏高度
    const { statusBarHeight, navBarHeight, navTotalHeight } = app.globalData;
    this.setData({
      statusBarHeight: statusBarHeight || 20,
      navBarHeight: navBarHeight || 44,
      navTotalHeight: navTotalHeight || 64
    });
    
    // 确保页面可滚动
    wx.pageScrollTo({
      scrollTop: 0,
      duration: 0
    });
  },
  
  // 接收导航栏高度变化事件
  onNavHeightChange: function(e) {
    const { totalHeight, statusBarHeight, navBarHeight } = e.detail;
    this.setData({
      navTotalHeight: totalHeight,
      statusBarHeight: statusBarHeight,
      navBarHeight: navBarHeight
    });
  },

  // 跳转到我的订单页面
  navToOrder: function() {
    wx.navigateTo({
      url: '/pages/order/list/list'
    });
  },

  // 跳转到我的地址页面
  navToAddress: function() {
    wx.navigateTo({
      url: '/pages/address/list/list'
    });
  },

  // 根据类型跳转到图文页面
  navToImagetext: function(e) {
    const type = e.currentTarget.dataset.type;

    // 根据类型查询图文数据
    sender.requestUrl({
      url: api.api_imagetext_list,
      method: 'GET',
      params: {
        type: type,
        pageNum: 1,
        pageSize: 1
      }
    }, (data) => {
      if (data && data.list && data.list.length > 0) {
        // 有数据，跳转到图文详情页
        wx.navigateTo({
          url: '/pages/imagetext/imagetext?id=' + data.list[0].id
        });
      } else {
        // 无数据，提示用户
        wx.showToast({
          title: '暂无相关内容',
          icon: 'none'
        });
      }
    });
  }
}) 