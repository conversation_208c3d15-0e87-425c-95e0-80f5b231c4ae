.custom-nav {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  box-sizing: border-box;
  z-index: 999;
}

.nav-status {
  width: 100%;
  background-color: var(--bg-color);
}

.nav-content {
  font-size: 18px;
  color: #333;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  position: relative;
  background-color: var(--bg-color);
}

.back-btn {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.back-icon {
  width: 22px;
  height: 22px;
}

.title {
  width: 100%;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 0 50px;
}

.right-btn {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.right-text {
  font-size: 18px;
  color: inherit;
  padding: 0 10px;
}
