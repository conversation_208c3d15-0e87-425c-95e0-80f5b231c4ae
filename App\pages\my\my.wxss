.nav-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

/* 用户信息区域 */
.user-info-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.avatar-container {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #ffffff;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.avatar {
  width: 100%;
  height: 100%;
}

.user-phone {
  margin-top: 15px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* 邀请好友区域 */
.invite-banner {
  padding: 15px;
  background-color: #fff;
  border-radius: 10px;
  display: flex;
  align-items: center;
  margin-top: 20rpx;
}

.invite-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.invite-banner text {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.invite-button {
  background-color: #FF6633;
  color: white;
  font-size: 12px;
  padding: 5px 10px;
  border-radius: 15px;
}

/* 功能列表 */
.function-list, .about-section {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  margin: 25rpx 0;
}

.function-item {
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  border-bottom: 1px solid #f5f5f5;
  position: relative;
}

.function-item:last-child {
  border-bottom: none;
}

.function-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.function-item text {
  flex: 1;
  font-size: 14px;
  color: #333;
}

/* 箭头图标样式 */
.arrow-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-img {
  width: 16px;
  height: 16px;
} 