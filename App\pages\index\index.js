var api = require('../../utils/api.js')
var sender = require('../../utils/sender.js')
const app = getApp();
var pageNum = 1;
var pageSize = 10;

Page({
  data: {
    CDN_IMAGE: api.CDN_IMAGE,
    banners: []
  },

  onLoad: function(options) {
    
    var loginStatus = wx.getStorageSync("LoginStatus")
    var that = this;
    if (loginStatus) {
      that.getBannerList()
    } else {
      app.userInfoReadyCallback = res => {
        that.getBannerList()
      }
    }
  },
  
  onShow: function() {
  
  },
  
  getBannerList(){
    var that = this
    sender.requestUrl({
      url: api.api_imagetext_list,
      method: 'GET',
      params: {
        pageNum: pageNum,
        pageSize: pageSize,
        type: 'banner'
      }
    }, function(data) {
      if (data && data.list) {
        that.setData({
          banners: data.list
        });
      }
    });
  },
  
  // 跳转到打印教程
  toImagetext(res) {
    console.log(res.currentTarget.dataset.type)
    sender.requestUrl({
      url: api.api_imagetext_list,
      method: 'GET',
      params: {
        type: res.currentTarget.dataset.type,
        pageNum: 1,
        pageSize: 1
      }
    }, function(data) {
      if (data && data.list && data.list.length > 0) {
        wx.navigateTo({
          url: '/pages/imagetext/imagetext?id=' + data.list[0].id
        });
      } else {
        wx.showToast({
          title: '暂无教程',
          icon: 'none'
        });
      }
    });
  },
  
  // 跳转到上传打印页面
  toUploadPrint(e) {
    const type = e.currentTarget.dataset.type;
    wx.navigateTo({
      url: '/pages/print/upload/upload?type=' + type
    });
  }
})