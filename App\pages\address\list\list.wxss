.container {
  padding: 0 0 120rpx 0;
  background-color: #f7f7f7;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 滚动区域 */
.address-scroll {
  flex: 1;
  height: calc(100vh - 120rpx - 130rpx); /* 减去顶部导航和底部按钮的高度 */
}

/* 地址列表样式 */
.address-list {
  padding: 20rpx;
}

.address-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.address-item:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.address-info {
  margin-bottom: 20rpx;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 20rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.tag {
  font-size: 22rpx;
  color: #ff6633;
  background-color: rgba(255, 102, 51, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  margin-left: 20rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.address-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1rpx solid #f5f5f5;
  padding-top: 20rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  margin-left: 30rpx;
  padding: 6rpx 0;
}

.action-btn .iconfont {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.action-btn text {
  font-size: 26rpx;
}

.edit .iconfont {
  color: #3a75fc;
}

.edit text {
  color: #3a75fc;
}

.delete .iconfont {
  color: #ff4d4f;
}

.delete text {
  color: #ff4d4f;
}

/* 空地址提示 */
.empty-address {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.iconfont.icon-address-empty {
  font-size: 120rpx;
  color: #ddd;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部按钮区域 */
.bottom-btns {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  display: flex;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.btn {
  flex: 1;
  height: 90rpx;
  font-size: 30rpx;
  border-radius: 45rpx;
  margin: 0 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.import-btn {
  background-color: #fff;
  color: #ff6633;
  border: 1rpx solid #ff6633;
}

.add-btn {
  background: linear-gradient(to right, #ff9933, #ff6633);
  color: #fff;
  border: none;
  box-shadow: 0 6rpx 10rpx rgba(255, 102, 51, 0.2);
} 