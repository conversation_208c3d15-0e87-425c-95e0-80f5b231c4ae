const app = getApp();
var api = require('../../../utils/api.js');
var sender = require('../../../utils/sender.js');

Page({
  data: {
    orderId: '',
    order: {}
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail(options.id);
    }
  },
  
  // 加载订单详情
  loadOrderDetail: function (id) {
    // 实际项目中应调用接口获取订单详情
    // sender.requestUrl({
    //   url: api.api_order_detail,
    //   method: 'GET',
    //   params: { id: id }
    // }, (data) => {
    //   wx.hideLoading();
    //   this.setData({
    //     order: data
    //   });
    // });
    
    // 模拟获取订单详情
    this._detailTimer = setTimeout(() => {
      // 从订单列表页获取数据
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      
      // 如果上一个页面是订单列表页
      if (prevPage && prevPage.route === 'pages/order/list/list') {
        const orderList = prevPage.data.orderList;
        const order = orderList.find(item => item.id == id);
        
        if (order) {
          // 添加订单地址信息
          order.address = {
            name: '张三',
            phone: '13800138000',
            province: '广东省',
            city: '深圳市',
            district: '南山区',
            detailAddress: '科技园88号大厦A座10层'
          };
          
          // 添加价格信息
          order.deliveryFee = 5.0;
          order.discountAmount = 0;
          order.payAmount = parseFloat((order.totalPrice + order.deliveryFee).toFixed(2));
          
          this.setData({
            order: order
          });
        }
      } else {
        // 直接进入详情页，模拟数据
        const mockOrder = {
          id: id,
          status: 3,
          statusText: '已完成',
          createTime: '2025-06-01 12:34:56',
          totalPrice: 12.8,
          deliveryFee: 5.0,
          discountAmount: 0,
          payAmount: 17.8,
          address: {
            name: '张三',
            phone: '13800138000',
            province: '广东省',
            city: '深圳市',
            district: '南山区',
            detailAddress: '科技园88号大厦A座10层'
          },
          items: [
            {
              id: 1,
              name: '打印文件1.doc',
              params: 'A4/双面/黑白/普通纸/不装订/共10页',
              price: 3.5,
              quantity: 2
            },
            {
              id: 2,
              name: '演示文稿.ppt',
              params: 'A4/单面/彩印/高端纸/装订/共20页',
              price: 5.8,
              quantity: 1
            }
          ]
        };
        
        this.setData({
          order: mockOrder
        });
      }
    }, 500);
  },
  
  // 页面卸载时清理定时器，避免异步回调影响导航
  onUnload: function() {
    if (this._detailTimer) {
      clearTimeout(this._detailTimer);
    }
  },
  
  // 取消订单
  cancelOrder: function () {
    wx.showModal({
      title: '提示',
      content: '确定要取消该订单吗？',
      success: (res) => {
        if (res.confirm) {
          // 实际项目中应调用接口取消订单
          // sender.requestUrl({
          //   url: api.api_order_cancel,
          //   method: 'POST',
          //   params: { id: this.data.orderId }
          // }, (data) => {
          //   wx.hideLoading();
          //   wx.navigateBack();
          // });
          
          // 模拟取消订单
          setTimeout(() => {
            wx.showToast({
              title: '订单已取消',
              icon: 'success'
            });
            
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          }, 500);
        }
      }
    });
  },
  
  // 支付订单
  payOrder: function () {
    // 实际项目中应调用支付接口
    // sender.requestUrl({
    //   url: api.api_order_pay,
    //   method: 'POST',
    //   params: { id: this.data.orderId }
    // }, (data) => {
    //   wx.hideLoading();
    //   // 调用微信支付
    //   wx.requestPayment({
    //     ...data,
    //     success: (res) => {
    //       wx.showToast({
    //         title: '支付成功',
    //         icon: 'success'
    //       });
    //       
    //       setTimeout(() => {
    //         wx.navigateBack();
    //       }, 1500);
    //     },
    //     fail: (err) => {
    //       wx.showToast({
    //         title: '支付失败',
    //         icon: 'none'
    //       });
    //     }
    //   });
    // });
    
    // 模拟支付
    setTimeout(() => {
      wx.showToast({
        title: '支付成功',
        icon: 'success'
      });
      
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 1000);
  }
}) 