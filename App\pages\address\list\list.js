const app = getApp();
var api = require('../../../utils/api.js');
var sender = require('../../../utils/sender.js');

Page({
  data: {
    addressList: [],
    loading: false,
    pageNum: 1,
    pageSize: 10,
    userId: null
  },

  onLoad: function (options) {
    // 获取当前用户ID
    const userInfo = wx.getStorageSync('UserInfo');
    this.setData({ userId: userInfo.id });
    this.loadAddressList();
  },
  
  onShow: function() {
    // 每次显示页面时刷新地址列表
    this.loadAddressList();
  },
  
  // 加载地址列表
  loadAddressList: function () {
    this.setData({ loading: true });
    sender.requestUrl({
      url: api.api_address_page,
      method: 'GET',
      params: {
        userId: this.data.userId,
        pageNum: this.data.pageNum,
        pageSize: this.data.pageSize
      }
    }, (data) => {
      this.setData({
        addressList: data.list,
        loading: false
      });
    });
  },
  
  // 导入微信地址
  importWechatAddress: function () {
    wx.chooseAddress({
      success: (res) => {
        // 微信地址导入成功
        const wxAddress = {
          recipient: res.userName,
          phone: res.telNumber,
          province: res.provinceName,
          city: res.cityName,
          county: res.countyName,
          detail: res.detailInfo,
          postalCode: res.postalCode,
          nationalCode: res.nationalCode,
          isDefault: false,
          userId: this.data.userId
        };
        
        // 实际项目中应该调用接口保存地址
        sender.requestUrl({
          url: api.api_address_add,
          method: 'POST',
          data: wxAddress
        }, () => {
          this.loadAddressList();
          wx.showToast({ title: '导入成功', icon: 'success' });
        });
      },
      fail: (err) => {
        // 用户取消或授权失败
        if (err.errMsg !== 'chooseAddress:fail cancel') {
          wx.showModal({
            title: '提示',
            content: '获取微信地址失败，请授权通讯地址权限',
            confirmText: '去授权',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.address']) {
                      wx.showToast({
                        title: '授权成功，请重新操作',
                        icon: 'none'
                      });
                    }
                  }
                });
              }
            }
          });
        }
      }
    });
  },
  
  // 新增地址
  addNewAddress: function () {
    wx.navigateTo({
      url: '/pages/address/edit/edit'
    });
  },
  
  // 编辑地址
  editAddress: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/address/edit/edit?id=' + id
    });
  },
  
  // 删除地址
  deleteAddress: function (e) {
    const id = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: '提示',
      content: '确定要删除该地址吗？',
      success: (res) => {
        if (res.confirm) {
          sender.requestUrl({
            url: api.api_address_delete,
            method: 'DELETE',
            params: { id: id }
          }, () => {
            this.loadAddressList();
            wx.showToast({ title: '删除成功', icon: 'success' });
          });
        }
      }
    });
  },
  
  // 选择地址
  selectAddress: function (e) {
    const id = e.currentTarget.dataset.id;
    const address = this.data.addressList.find(item => item.id === id);
    
    // 如果是从订单页面选择地址
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];
    
    if (prevPage && prevPage.route === 'pages/order/confirm/confirm') {
      // 向订单确认页传递选中的地址
      prevPage.setData({
        selectedAddress: address
      });
      wx.navigateBack();
    }
  }
}) 