const app = getApp();
var api = require('../../../utils/api.js');
var sender = require('../../../utils/sender.js');

Page({
  data: {
    currentTab: 0, // 当前选中的tab
    tabs: ['全部', '待付款', '待配送', '已完成'],
    orderList: [], // 订单列表
    loading: false,
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    // 模拟订单数据
    mockOrders: [
      {
        id: '202506010001',
        status: 3, // 0待付款，1待配送，2配送中，3已完成
        statusText: '已完成',
        createTime: '2025-06-01 12:34:56',
        totalPrice: 12.8,
        items: [
          {
            id: 1,
            name: '打印文件1.doc',
            params: 'A4/双面/黑白/普通纸/不装订/共10页',
            price: 3.5,
            quantity: 2
          },
          {
            id: 2,
            name: '演示文稿.ppt',
            params: 'A4/单面/彩印/高端纸/装订/共20页',
            price: 5.8,
            quantity: 1
          }
        ]
      },
      {
        id: '202506010002',
        status: 1, // 0待付款，1待配送，2配送中，3已完成
        statusText: '待配送',
        createTime: '2025-06-01 09:12:34',
        totalPrice: 6.8,
        items: [
          {
            id: 3,
            name: 'GPU目标检测.doc',
            params: 'A4/双面/彩印/护眼纸/订书钉/共6页',
            price: 6.8,
            quantity: 1
          }
        ]
      },
      {
        id: '202506010003',
        status: 0, // 0待付款，1待配送，2配送中，3已完成
        statusText: '待付款',
        createTime: '2025-06-01 08:45:12',
        totalPrice: 15.6,
        items: [
          {
            id: 4,
            name: '毕业论文终稿.doc',
            params: 'A4/双面/黑白/普通纸/胶装/共50页',
            price: 15.6,
            quantity: 1
          }
        ]
      }
    ],
    manageMode: false,
    selectedOrders: []
  },

  onLoad: function (options) {
    this.loadOrderList();
  },
  
  // 切换Tab
  switchTab: function (e) {
    const index = e.currentTarget.dataset.index;
    if (this.data.currentTab === index) {
      return;
    }
    
    this.setData({
      currentTab: index,
      orderList: [],
      pageNum: 1,
      hasMore: true
    });
    
    this.loadOrderList();
  },
  
  // 加载订单列表
  loadOrderList: function () {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }
    
    this.setData({
      loading: true
    });
    
    // 模拟请求后端API
    setTimeout(() => {
      let orders = [];
      const status = this.data.currentTab;
      
      if (status === 0) { // 全部
        orders = this.data.mockOrders;
      } else { // 筛选对应状态
        orders = this.data.mockOrders.filter(item => item.status === status - 1);
      }
      
      this.setData({
        orderList: [...this.data.orderList, ...orders],
        pageNum: this.data.pageNum + 1,
        hasMore: orders.length === this.data.pageSize,
        loading: false
      });
    }, 500);
  },
  
  // 查看订单详情
  viewOrderDetail: function (e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/order/detail/detail?id=' + orderId
    });
  },
  
  // 下拉刷新
  onPullDownRefresh: function () {
    this.setData({
      orderList: [],
      pageNum: 1,
      hasMore: true
    });
    
    this.loadOrderList();
    wx.stopPullDownRefresh();
  },
  
  // 上拉加载更多
  onReachBottom: function () {
    this.loadOrderList();
  },
  
  // 切换管理模式
  toggleManage: function () {
    this.setData({ manageMode: !this.data.manageMode, selectedOrders: [] });
  },
  
  // 管理模式下选中订单
  onCheckboxGroupChange: function (e) {
    this.setData({ selectedOrders: e.detail.value });
  },
  
  // 批量取消选中订单
  cancelSelectedOrders: function () {
    if (this.data.selectedOrders.length === 0) {
      wx.showToast({ title: '请先选择订单', icon: 'none' });
      return;
    }
    wx.showModal({
      title: '提示',
      content: '确定要取消选中的订单吗？',
      success: (res) => {
        if (res.confirm) {
          const ids = this.data.selectedOrders;
          const newList = this.data.orderList.filter(item => ids.indexOf(item.id) === -1);
          this.setData({ orderList: newList, manageMode: false, selectedOrders: [] });
          wx.showToast({ title: '订单已取消', icon: 'success' });
        }
      }
    });
  }
}) 