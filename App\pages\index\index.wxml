<!-- 使用自定义导航栏 -->
<view class="container">
  <custom-nav title="云打印" color="#333"></custom-nav>
  <!-- 广告横幅 -->
  <view class="slogan-banner">
    4大自营工厂 | 9年专注打印 | 累计订单超690万单！
  </view>

  <!-- 轮播图区域 -->
  <view class="swiper-container">
    <swiper class="banner-swiper" indicator-dots="{{false}}" autoplay="{{true}}" interval="3000" duration="500" circular="{{true}}">
      <block wx:for="{{banners}}" wx:key="id">
        <swiper-item>
          <!-- 图片轮播项 -->
          <view class="banner-item">
            <image src="{{CDN_IMAGE + item.imageUrl}}" mode="aspectFill" class="banner-image"></image>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <!-- 功能快捷入口 -->
  <view class="function-menu">
    <view class="menu-item" bindtap="toImagetext" data-type="print_tu">
      <image src="/images/icon/icon_print_tutorial.png" class="menu-icon"></image>
      <text>打印教程</text>
    </view>
    <view class="menu-item" bindtap="toImagetext" data-type="test_paper">
      <image src="/images/icon/icon_test_paper.png" class="menu-icon"></image>
      <text>试卷打印</text>
    </view>
    <view class="menu-item">
      <image src="/images/icon/icon_leaflet.png" class="menu-icon"></image>
      <text>传单画册</text>
    </view>
    <view class="menu-item">
      <image src="/images/icon/icon_partner.png" class="menu-icon"></image>
      <text>商务合作</text>
    </view>
    <view class="menu-item">
      <image src="/images/icon/icon_evaluate.png" class="menu-icon"></image>
      <text>用户晒单</text>
    </view>
  </view>

  <!-- 核心服务模块 -->
  <view class="service-grid">
    <view class="service-row">
      <!-- 文件打印 -->
      <view class="service-cell orange">
        <view class="service-content">
          <view class="service-title">文件打印</view>
          <view class="service-desc">上传文件→打印设置→自动计价</view>
        </view>
        <view class="service-button">
          <button bindtap="toUploadPrint" data-type="file">
            <image src="/images/icon/icon_upload.png" class="button-icon"></image>
            上传打印
          </button>
        </view>
      </view>
      
      <!-- 中间分隔线 -->
      <view class="service-divider"></view>
      
      <!-- 拼团打印 -->
      <view class="service-cell green">
        <view class="service-content">
          <view class="service-title">图片打印</view>
          <view class="service-desc">上传文件→打印设置→自动计价</view>
        </view>
        <view class="service-button">
          <button bindtap="toUploadPrint" data-type="image">
            <image src="/images/icon/icon_share.png" class="button-icon"></image>
            上传图片
          </button>
        </view>
      </view>
    </view>
  </view>

</view>