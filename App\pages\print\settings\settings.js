var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();



Page({
  data: {
    printFile: {},
    pickerRange: [],
    pickerValue: [],
    attrList: [],
    specsList: [],
    selectedList: []
  },

  onLoad: function (options) {
    var id = options.id
    this.getTempFileList(id)
    this.loadPrintAttributes()
    this.loadPrintSpecs()
  },
  getTempFileList(id) {
    var that = this
    sender.requestUrl({
      url: api.api_temp_file,
      method: 'GET',
      params: {
        id: id
      }
    }, function (data) {

      var pageArray = []

      for (var i = 1; i <= data.totalPage; i++) {
        pageArray.push(i)
      }

      var pickerValue = [data.pageNum - 1, data.pageSize - 1]
      var pickerRange = [pageArray, pageArray]

      that.setData({
        printFile: data,
        pickerValue: pickerValue,
        pickerRange: pickerRange
      });

    });
  },
  // 加载打印属性
  loadPrintAttributes: function () {
    var that = this
    sender.requestUrl({
      url: api.api_print_attr,
      method: 'GET'
    }, function (data) {
      that.setData({
        attrList: data
      });
      // 属性加载完成后，检查是否需要初始化禁用状态
      that.initializeDisabledStatus()
    });
  },
  //加载打印规格
  loadPrintSpecs() {
    var that = this
    sender.requestUrl({
      url: api.api_print_specs,
      method: 'GET'
    }, function (data) {
      that.setData({
        specsList: data
      });
      // 规格加载完成后，检查是否需要初始化禁用状态
      that.initializeDisabledStatus()
    });
  },

  // 初始化禁用状态
  initializeDisabledStatus() {
    var attrList = this.data.attrList
    var specsList = this.data.specsList

    // 确保两个数据都已加载
    if (attrList.length > 0 && specsList.length > 0) {
      var selectedMap = this.buildSelectedMap(attrList)
      this.updateDisabledStatus(attrList, specsList, selectedMap)
    }
  },
  // 减少数量
  decreaseQuantity: function () {
    const printFile = this.data.printFile;

    if (printFile.quantity > 1) {
      printFile.quantity--;
      this.setData({
        printFile: printFile
      });
      this.calculateTotal();
    }
  },

  // 增加数量
  increaseQuantity: function () {
    const printFile = this.data.printFile;
    printFile.quantity++;
    this.setData({
      printFile: printFile
    });
    this.calculateTotal();
  },
  calculateTotal() {

  },
  selectPaperSize(e) {
    var attrIndex = e.currentTarget.dataset.attrIndex
    var valueIndex = e.currentTarget.dataset.valueIndex
    var attrList = this.data.attrList
    var specsList = this.data.specsList

    // 检查选项是否被禁用
    var selectedValue = attrList[attrIndex].attrValues[valueIndex]
    if (selectedValue.disabled) {
      console.log('选项已禁用，无法选择:', selectedValue.attrValue)
      return
    }

    attrList[attrIndex].selected = selectedValue.attrValue

    this.setData({
      attrList: attrList
    })

    // 构建当前已选择的属性映射
    var selectedMap = this.buildSelectedMap(attrList)
    this.updateDisabledStatus(attrList, specsList, selectedMap)

    // 重新计算价格
    this.calculateTotal()
  },

  /**
   * 构建当前已选择的属性映射
   * @param {Array} attrList - 属性列表
   * @returns {Object} 已选择的属性映射 { 属性名: 值 }
   */
  buildSelectedMap(attrList) {
    var selectedMap = {}
    attrList.forEach(function(attr) {
      if (attr.selected) {
        selectedMap[attr.attrName] = attr.selected
      }
    })
    return selectedMap
  },

  /**
   * 更新每个规格值的 disabled 状态
   * @param {Array} attrList - 所有属性列表
   * @param {Array} specsList - 所有有效 SKU 组合
   * @param {Object} selectedMap - 当前已选的 { 属性名: 值 }
   */
  updateDisabledStatus(attrList, specsList, selectedMap) {
    // 遍历每个属性
    attrList.forEach(function(attr) {
      // 遍历每个属性值
      attr.attrValues.forEach(function(attrValue) {
        // 创建临时的选择映射，包含当前测试的属性值
        var tempSelectedMap = Object.assign({}, selectedMap)
        tempSelectedMap[attr.attrName] = attrValue.attrValue

        // 检查是否存在匹配的SKU
        var hasMatchingSku = false

        // 遍历所有可用的SKU规格
        for (var i = 0; i < specsList.length; i++) {
          var spec = specsList[i]

          // 解析规格字符串，例如: "大小:A4,颜色:黑白,打印方式:单面"
          var specMap = this.parseSpecString(spec.specs)

          // 检查当前选择是否与此SKU兼容
          var isCompatible = true
          for (var attrName in tempSelectedMap) {
            if (tempSelectedMap.hasOwnProperty(attrName)) {
              var selectedValue = tempSelectedMap[attrName]
              var specValue = specMap[attrName]

              // 如果SKU中包含此属性且值不匹配，则不兼容
              if (specValue && specValue !== selectedValue) {
                isCompatible = false
                break
              }
            }
          }

          // 如果找到兼容的SKU，标记为可用
          if (isCompatible) {
            hasMatchingSku = true
            break
          }
        }

        // 设置disabled状态
        attrValue.disabled = !hasMatchingSku
      }.bind(this))
    }.bind(this))

    // 更新页面数据
    this.setData({
      attrList: attrList
    })
  },

  /**
   * 解析规格字符串为属性映射
   * @param {String} specsString - 规格字符串，如 "大小:A4,颜色:黑白,打印方式:单面"
   * @returns {Object} 属性映射 { 属性名: 值 }
   */
  parseSpecString(specsString) {
    var specMap = {}

    if (!specsString) {
      return specMap
    }

    // 根据您的数据库截图，规格字符串格式类似 "大小:A4,颜色:黑白,打印方式:单面"
    // 但实际可能是用斜杠分隔，如 "A4/黑白/单面/普通纸/不装订"
    // 这里提供两种解析方式

    if (specsString.indexOf('/') > -1) {
      // 斜杠分隔格式: "A4/黑白/单面/普通纸/不装订"
      var parts = specsString.split('/')
      var attrNames = ['纸张大小', '颜色', '单双面', '纸张类型', '装订方式'] // 根据实际属性顺序调整

      parts.forEach(function(part, index) {
        if (index < attrNames.length && part.trim()) {
          specMap[attrNames[index]] = part.trim()
        }
      })
    } else if (specsString.indexOf(',') > -1) {
      // 逗号分隔格式: "大小:A4,颜色:黑白,打印方式:单面"
      var pairs = specsString.split(',')
      pairs.forEach(function(pair) {
        var keyValue = pair.split(':')
        if (keyValue.length === 2) {
          var key = keyValue[0].trim()
          var value = keyValue[1].trim()
          specMap[key] = value
        }
      })
    } else {
      // JSON格式或其他格式的处理
      try {
        specMap = JSON.parse(specsString)
      } catch (e) {
        console.warn('无法解析规格字符串:', specsString)
      }
    }

    return specMap
  },

  onPickerChange(e) {
    var pickerValue = e.detail.value
    var printFile = this.data.printFile
    printFile.pageNum = pickerValue[0] + 1
    printFile.pageSize = pickerValue[1] + 1
    this.setData({
      pickerValue: pickerValue,
      printFile: printFile
    })
  }
})