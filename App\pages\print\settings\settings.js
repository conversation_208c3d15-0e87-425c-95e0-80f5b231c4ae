var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();



Page({
  data: {
    printFile: {},
    pickerRange: [],
    pickerValue: [],
    attrList: [],
    specsList: [],
    selectedList: []
  },

  onLoad: function (options) {
    var id = options.id
    this.getTempFileList(id)
    this.loadPrintAttributes()
    this.loadPrintSpecs()
  },
  getTempFileList(id) {
    var that = this
    sender.requestUrl({
      url: api.api_temp_file,
      method: 'GET',
      params: {
        id: id
      }
    }, function (data) {

      var pageArray = []

      for (var i = 1; i <= data.totalPage; i++) {
        pageArray.push(i)
      }

      var pickerValue = [data.pageNum - 1, data.pageSize - 1]
      var pickerRange = [pageArray, pageArray]

      that.setData({
        printFile: data,
        pickerValue: pickerValue,
        pickerRange: pickerRange
      });

    });
  },
  // 加载打印属性
  loadPrintAttributes: function () {
    var that = this
    sender.requestUrl({
      url: api.api_print_attr,
      method: 'GET'
    }, function (data) {
      that.setData({
        attrList: data
      });
    });
  },
  //加载打印规格
  loadPrintSpecs() {
    var that = this
    sender.requestUrl({
      url: api.api_print_specs,
      method: 'GET'
    }, function (data) {
      that.setData({
        specsList: data
      });
    });
  },
  // 减少数量
  decreaseQuantity: function () {
    const printFile = this.data.printFile;

    if (printFile.quantity > 1) {
      printFile.quantity--;
      this.setData({
        printFile: printFile
      });
      this.calculateTotal();
    }
  },

  // 增加数量
  increaseQuantity: function () {
    const printFile = this.data.printFile;
    printFile.quantity++;
    this.setData({
      printFile: printFile
    });
    this.calculateTotal();
  },
  calculateTotal() {

  },
  selectPaperSize(e) {
    var attrIndex = e.currentTarget.dataset.attrIndex
    var valueIndex = e.currentTarget.dataset.valueIndex
    var attrList = this.data.attrList
    var specsList = this.data.specsList

    attrList[attrIndex].selected = attrList[attrIndex].attrValues[valueIndex].attrValue

    this.setData({
      attrList: attrList
    })

    this.updateDisabledStatus(attrList, specsList, selectedMap)
  },
  
  /**
   * 更新每个规格值的 disabled 状态
   * @param {Array} attrList - 所有属性列表
   * @param {Array} specsList - 所有有效 SKU 组合
   * @param {Map} selectedMap - 当前已选的 { 属性名: 值 }
   */
  updateDisabledStatus(attrList, specsList, selectedMap) {
   
  },

  onPickerChange(e) {
    var pickerValue = e.detail.value
    var printFile = this.data.printFile
    printFile.pageNum = pickerValue[0] + 1
    printFile.pageSize = pickerValue[1] + 1
    this.setData({
      pickerValue: pickerValue,
      printFile: printFile
    })
  }
})