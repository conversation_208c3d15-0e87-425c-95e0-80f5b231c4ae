var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();



Page({
  data: {
    printFile: {},
    pickerRange: [],
    pickerValue: [],
    attrList: [],
    specsList: [],
    selected: [],
    compatibilityMatrix: null // 邻接矩阵，存储属性值之间的兼容关系
  },

  onLoad: function (options) {
    var id = options.id
    this.getTempFileList(id)
    this.loadPrintAttributes()
    this.loadPrintSpecs()
  },
  getTempFileList(id) {
    var that = this
    sender.requestUrl({
      url: api.api_temp_file,
      method: 'GET',
      params: {
        id: id
      }
    }, function (data) {

      var pageArray = []

      for (var i = 1; i <= data.totalPage; i++) {
        pageArray.push(i)
      }

      var pickerValue = [data.pageNum - 1, data.pageSize - 1]
      var pickerRange = [pageArray, pageArray]

      that.setData({
        printFile: data,
        pickerValue: pickerValue,
        pickerRange: pickerRange
      });

    });
  },
  // 加载打印属性
  loadPrintAttributes: function () {
    var that = this
    sender.requestUrl({
      url: api.api_print_attr,
      method: 'GET'
    }, function (data) {
      that.setData({
        attrList: data
      });
      // 属性加载完成后，构建兼容性矩阵
      that.buildCompatibilityMatrix()
    });
  },
  //加载打印规格
  loadPrintSpecs() {
    var that = this
    sender.requestUrl({
      url: api.api_print_specs,
      method: 'GET'
    }, function (data) {
      that.setData({
        specsList: data
      });
      // 规格加载完成后，构建兼容性矩阵
      that.buildCompatibilityMatrix()
    });
  },

  /**
   * 构建兼容性矩阵（邻接矩阵）
   * 用于快速查找属性值之间的兼容关系
   */
  buildCompatibilityMatrix() {
    var attrList = this.data.attrList
    var specsList = this.data.specsList

    // 确保两个数据都已加载
    if (!attrList || attrList.length === 0 || !specsList || specsList.length === 0) {
      return
    }

    // 初始化兼容性矩阵
    var matrix = new Map()

    // 遍历所有SKU规格，构建兼容关系
    specsList.forEach(function(spec) {
      // 解析规格字符串，获取属性值数组
      var specValues = this.parseSpecToValues(spec.specs, attrList)
      console.log('解析规格:', spec.specs, '结果:', specValues)

      // 对于这个SKU中的每对属性值，标记为兼容
      for (var i = 0; i < specValues.length; i++) {
        for (var j = 0; j < specValues.length; j++) {
          if (i !== j) {
            var key1 = specValues[i]
            var key2 = specValues[j]

            // 确保矩阵中存在这个key
            if (!matrix.has(key1)) {
              matrix.set(key1, new Set())
            }

            // 添加兼容关系
            matrix.get(key1).add(key2)
          }
        }
      }
    }.bind(this))
    console.log('兼容性矩阵构建完成:', matrix)

    // 保存矩阵并初始化禁用状态
    this.setData({
      compatibilityMatrix: matrix
    })

    // 初始化禁用状态
    this.updateDisabledStatusWithMatrix()
  },
  // 减少数量
  decreaseQuantity: function () {
    const printFile = this.data.printFile;

    if (printFile.quantity > 1) {
      printFile.quantity--;
      this.setData({
        printFile: printFile
      });
      this.calculateTotal();
    }
  },

  // 增加数量
  increaseQuantity: function () {
    const printFile = this.data.printFile;
    printFile.quantity++;
    this.setData({
      printFile: printFile
    });
    this.calculateTotal();
  },
  calculateTotal() {
    let specsList = this.data.specsList
    let specs = this.data.selected.join("/")
    for(let item of specsList){
      if(item.specs == specs){
        console.log(item)
        let pageNum = this.data.pickerValue[0]
        let pageSize = this.data.pickerValue[1]
        let totalPage = pageSize - pageNum + 1
        let quantity = this.data.printFile.quantity
        let amount = item.amount == null ? 0 : item.amount
        let totalAmount = amount * quantity * totalPage
        let printFile = this.data.printFile
        printFile.skuId = item.id
        this.setData({
          price: totalAmount,
          printFile: printFile
        })
      }
    }
  },
  selectPaperSize(e) {
    var attrIndex = e.currentTarget.dataset.attrIndex
    var valueIndex = e.currentTarget.dataset.valueIndex
    var attrList = this.data.attrList

    // 检查选项是否被禁用
    var selectedValue = attrList[attrIndex].attrValues[valueIndex]
    if (selectedValue.disabled) {
      console.log('选项已禁用，无法选择:', selectedValue.attrValue)
      return
    }

    attrList[attrIndex].selected = selectedValue.attrValue

    this.setData({
      attrList: attrList
    })

    // 使用邻接矩阵更新禁用状态
    this.updateDisabledStatusWithMatrix()

    // 重新计算价格
    let flag = true
    let selected = []
    for(let item of attrList){
      selected.push(item.selected)
      if(!item.selected){
          flag = false
      }
    }
  
    if(flag){
      this.setData({
        selected: selected
      })
      this.calculateTotal()
    }
    
  },

  /**
   * 解析规格字符串为属性值数组
   * @param {String} specsString - 规格字符串
   * @param {Array} attrList - 属性列表，用于确定属性顺序
   * @returns {Array} 属性值数组，格式为 ["属性名:属性值", ...]
   */
  parseSpecToValues(specsString, attrList) {
    var values = []

    if (!specsString || !attrList) {
      return values
    }

    console.log('解析规格字符串:', specsString)

    if (specsString.indexOf('/') > -1) {
      // 斜杠分隔格式: "A4/黑白/单面/普通纸/不装订"
      var parts = specsString.split('/')

      // 根据属性列表的顺序来匹配
      attrList.forEach(function(attr, index) {
        if (index < parts.length && parts[index].trim()) {
          var key = attr.attrName + ':' + parts[index].trim()
          values.push(key)
        }
      })
    } else if (specsString.indexOf(',') > -1) {
      // 逗号分隔格式: "大小:A4,颜色:黑白,打印方式:单面"
      var pairs = specsString.split(',')
      pairs.forEach(function(pair) {
        var keyValue = pair.split(':')
        if (keyValue.length === 2) {
          var key = keyValue[0].trim() + ':' + keyValue[1].trim()
          values.push(key)
        }
      })
    } else {
      // 尝试JSON格式
      try {
        var specObj = JSON.parse(specsString)
        for (var attrName in specObj) {
          if (specObj.hasOwnProperty(attrName)) {
            var key = attrName + ':' + specObj[attrName]
            values.push(key)
          }
        }
      } catch (e) {
        console.warn('无法解析规格字符串:', specsString)
      }
    }

    console.log('解析结果:', values)
    return values
  },

  /**
   * 使用邻接矩阵更新每个规格值的 disabled 状态
   */
  updateDisabledStatusWithMatrix() {
    var attrList = this.data.attrList
    var matrix = this.data.compatibilityMatrix

    if (!attrList || !matrix) {
      console.log('数据未准备好，跳过更新禁用状态')
      return
    }

    console.log('使用邻接矩阵更新禁用状态...')

    // 获取当前所有已选择的属性值
    var selectedKeys = []
    attrList.forEach(function(attr) {
      if (attr.selected) {
        var key = attr.attrName + ':' + attr.selected
        selectedKeys.push(key)
      }
    })

    console.log('当前已选择的属性值:', selectedKeys)

    // 遍历每个属性
    attrList.forEach(function(attr) {
      // 遍历每个属性值
      attr.attrValues.forEach(function(attrValue) {
        var currentKey = attr.attrName + ':' + attrValue.attrValue

        // 如果这个属性值已经被选中，则不禁用
        if (selectedKeys.indexOf(currentKey) !== -1) {
          attrValue.disabled = false
          return
        }

        // 检查这个属性值是否与所有已选择的属性值都兼容
        var isCompatible = true

        for (var i = 0; i < selectedKeys.length; i++) {
          var selectedKey = selectedKeys[i]

          // 跳过同一属性的其他值
          var selectedAttrName = selectedKey.split(':')[0]
          if (selectedAttrName === attr.attrName) {
            continue
          }

          // 检查兼容性
          if (!matrix.has(currentKey) || !matrix.get(currentKey).has(selectedKey)) {
            console.log('不兼容:', currentKey, 'vs', selectedKey)
            isCompatible = false
            break
          }
        }

        // 设置disabled状态
        attrValue.disabled = !isCompatible

        if (attrValue.disabled) {
          console.log('禁用选项:', currentKey)
        }
      })
    })

    // 更新页面数据
    this.setData({
      attrList: attrList
    })

    console.log('禁用状态更新完成')
  },
  submitPrint(){
    console.log(this.data.printFile)
    
    sender.requestUrl({
      url: api.api_temp_file,
      method: 'PUT',
      data: this.data.printFile
    }, function (data) {
      wx.navigateBack({
        delta: 1
      })
    });
  },

  onPickerChange(e) {
    var pickerValue = e.detail.value
    var printFile = this.data.printFile
    printFile.pageNum = pickerValue[0] + 1
    printFile.pageSize = pickerValue[1] + 1
    this.setData({
      pickerValue: pickerValue,
      printFile: printFile
    })
    this.calculateTotal()
  }
})