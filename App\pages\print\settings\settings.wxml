<view class="container">
  <custom-nav title="打印设置" color="#333" showBack="{{true}}"></custom-nav>
  
  <!-- 文件信息区域 -->
  <view class="file-info-section">
    <view class="file-header">
      <!-- 根据文件类型显示对应图标 -->
      <view class="file-icon-wrapper">
        <image wx:if="{{printFile.type === 'pdf'}}" class="file-icon" src="/images/icon/pdf_icon.png" mode="aspectFit"></image>
        <image wx:elif="{{printFile.type === 'doc' || printFile.type === 'docx'}}" class="file-icon" src="/images/icon/word_icon.png" mode="aspectFit"></image>
        <image wx:elif="{{printFile.type === 'xls' || printFile.type === 'xlsx'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
        <image wx:elif="{{printFile.type === 'ppt' || printFile.type === 'pptx'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
        <image wx:elif="{{printFile.type === 'txt'}}" class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
        <image wx:elif="{{printFile.type === 'jpg' || printFile.type === 'jpeg' || printFile.type === 'png' || printFile.type === 'GIF'}}" class="file-icon" src="/images/icon/image_icon.png" mode="aspectFit"></image>
        <image wx:else class="file-icon" src="/images/icon/icon_sys_file.png" mode="aspectFit"></image>
      </view>

      <view class="file-info">
        <view class="file-name">{{printFile.fileName}}</view>
      </view>
    </view>
  </view>
  
  <!-- 打印设置区域 -->
  <view class="settings-section">
    <!-- 加载状态 -->
    <view wx:if="{{isLoading}}" class="loading-container">
      <view class="loading-text">正在加载打印选项...</view>
    </view>

    <!-- 错误状态 -->
    <view wx:elif="{{loadError}}" class="error-container">
      <view class="error-text">加载打印选项失败</view>
      <button class="retry-btn" bindtap="retryLoadData">重试</button>
    </view>

    <!-- 设置选项（加载完成后显示） -->
    <view wx:else>
      <!-- 份数设置 -->
      <view class="setting-item">
        <view class="setting-label">份数</view>
        <view class="setting-control">
          <view class="quantity-control">
            <view class="setting-quantity-btn minus" bindtap="decreaseQuantity">-</view>
            <view class="quantity-value">{{printFile.quantity}}</view>
            <view class="setting-quantity-btn plus" bindtap="increaseQuantity">+</view>
          </view>
        </view>
      </view>
    
    <!-- 打印范围 -->
    <view class="setting-item">
      <view class="setting-label">打印范围</view>
      <view class="setting-control">
        <picker mode="multiSelector" 
          bindchange="onPickerChange" 
          bindcolumnchange="onColumnChange"
          range-key="{{item}}"
          value="{{pickerValue}}"
          range="{{pickerRange}}">
          <view class="range-control">
            {{printFile.pageNum}}-{{printFile.pageSize}}
            <view class="dropdown-icon">▼</view>
          </view>
        </picker>
      
      </view>
    </view>
    
    <!-- 纸张大小 -->
    <view class="setting-item" wx:for="{{attrList}}" wx:key="attrIndex" wx:for-item="attr" wx:for-index="attrIndex">
      <view class="setting-label">{{attr.attrName}}</view>
      <view class="setting-control">
        <view class="option-group">
          <view wx:for="{{attr.attrValues}}" wx:key="valueIndex" wx:for-item="value"  wx:for-index="valueIndex"
           class="option-item {{attr.selected == value.attrValue ? 'active' : ''}}  {{value.disabled ? 'disabled' : ''}}"
          data-attr-index="{{attrIndex}}"
          data-value-index="{{valueIndex}}"
          bindtap="selectPaperSize"
          >
            {{value.attrValue}}
          </view>
        </view>
      </view>
    </view>

    </view> <!-- 关闭 wx:else -->
  </view>

  <!-- 底部操作区 -->
  <view class="bottom-action">
    <view class="total-price">
      合计: <text class="price-symbol">¥</text><text class="price-value">{{price}}</text>
    </view>
    <button class="confirm-btn" bindtap="submitPrint">确定</button>
  </view>
  
</view> 