<view class="container">
  <custom-nav title="我的地址" color="#333" showBack="{{true}}"></custom-nav>
  
  <!-- 地址列表 -->
  <scroll-view scroll-y="true" class="address-scroll">
    <view class="address-list" wx:if="{{addressList.length > 0}}">
      <view class="address-item" wx:for="{{addressList}}" wx:key="id" bindtap="selectAddress" data-id="{{item.id}}">
        <view class="address-info">
          <view class="address-header">
            <text class="name">{{item.recipient}}</text>
            <text class="phone">{{item.phone}}</text>
            <text class="tag" wx:if="{{item.isDefault}}">默认</text>
          </view>
          <view class="address-detail">{{item.province}}{{item.city}}{{item.county}}{{item.town}}{{item.detail}}</view>
        </view>
        <view class="address-actions">
          <view class="action-btn edit" catchtap="editAddress" data-id="{{item.id}}">
            <text>编辑</text>
          </view>
          <view class="action-btn delete" catchtap="deleteAddress" data-id="{{item.id}}">
            <text>删除</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 无地址提示 -->
    <view class="empty-address" wx:else>
      <view class="empty-icon">
        <view class="iconfont icon-address-empty"></view>
      </view>
      <text class="empty-text">您还没有添加地址</text>
    </view>
  </scroll-view>
  
  <!-- 底部按钮区 -->
  <view class="bottom-btns">
    <button class="btn import-btn" bindtap="importWechatAddress">导入微信地址</button>
    <button class="btn add-btn" bindtap="addNewAddress">新增地址</button>
  </view>
</view> 