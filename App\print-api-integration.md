# 打印设置接口对接完成说明

## 已完成的接口对接

### 1. API配置更新 ✅
- 更新了 `App/utils/api.js`，添加了新的打印相关接口：
  - `api_print_specs`: 获取打印规格列表
  - `api_print_attr`: 获取打印属性列表
  - `api_order_detail`: 上传打印文件

### 2. 数据结构重构 ✅
- 移除了硬编码的规格数组，改为从API动态获取
- 重新设计了数据结构，支持API返回的数据格式：
  - `PrintSku`: 包含 `id`, `specs`, `amount`, `image`, `note`
  - `PrintAttr`: 包含 `id`, `attrName`, `attrValues[]`

### 3. 接口对接实现 ✅

#### 获取打印属性 (`/users-api/v1/print/attr`)
- 实现了 `loadPrintAttributes()` 方法
- 根据属性名称自动映射到对应的选项数组
- 支持的属性类型：
  - 纸张大小 (paperSize)
  - 单双面 (printSide)
  - 打印方向 (direction)
  - 颜色模式 (colorType)
  - 纸张类型 (paperType)
  - 装订方式 (binding)
  - 多页合一 (multiPage)

#### 获取打印规格 (`/users-api/v1/print/specs`)
- 实现了 `loadPrintSpecs()` 方法
- 获取所有可用的打印规格组合和价格

#### 规格匹配算法
- 实现了智能规格匹配逻辑
- 支持完全匹配和部分匹配（70%匹配度）
- 构建规格字符串进行比较

#### 价格计算
- 基于API返回的 `amount` 字段计算价格
- 公式：总价 = 单价 × 页数 × 份数

#### 提交打印文件 (`/users-api/v1/order/detail`)
- 实现了 `submitPrint()` 方法
- 提交数据包含：
  - `fileName`: 文件名
  - `filePath`: 文件路径
  - `pageNum`: 起始页
  - `pageSize`: 页数
  - `quantity`: 份数
  - `skuId`: 匹配的规格ID

### 4. 错误处理优化 ✅
- 更新了 `sender.js`，支持错误回调
- 添加了API加载失败时的默认值处理
- 完善了用户提示信息

## 使用流程

### 1. 页面加载时
```javascript
onLoad() -> loadPrintData() -> [loadPrintAttributes(), loadPrintSpecs()]
```

### 2. 用户选择打印选项时
```javascript
selectOption() -> calculatePrice() -> findMatchingSpecification()
```

### 3. 提交打印时
```javascript
submitPrint() -> 调用 /users-api/v1/order/detail 接口
```

## 数据流程

### API数据结构示例

#### 打印属性响应 (`PrintAttr`)
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "attrName": "纸张大小",
      "attrValues": [
        {"attrValue": "A4", "description": "标准A4纸张"},
        {"attrValue": "A3", "description": "A3大尺寸纸张"}
      ]
    }
  ]
}
```

#### 打印规格响应 (`PrintSku`)
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "specs": "A4/黑白/单面/普通纸/不装订",
      "amount": 0.10,
      "image": "spec1.jpg",
      "note": "经济实惠的基础打印"
    }
  ]
}
```

### 提交数据结构 (`PrintFile`)
```json
{
  "fileName": "document.pdf",
  "filePath": "/uploads/document.pdf",
  "pageNum": 1,
  "pageSize": 10,
  "quantity": 2,
  "skuId": 1
}
```

## 测试建议

### 1. 接口连通性测试
- 确保后端接口正常运行
- 验证认证token有效性
- 检查接口返回数据格式

### 2. 功能测试
- 测试属性选项是否正确加载
- 验证规格匹配算法准确性
- 确认价格计算正确性
- 测试提交功能

### 3. 异常处理测试
- 网络断开时的处理
- API返回错误时的处理
- 数据格式异常时的处理

## 注意事项

1. **认证要求**: 所有接口都需要有效的Authorization token
2. **数据映射**: 属性名称需要与后端返回的 `attrName` 保持一致
3. **规格匹配**: 当前使用字符串匹配，可根据实际需求调整算法
4. **错误处理**: 建议在生产环境中添加更详细的错误日志

## 后续优化建议

1. 添加缓存机制，避免重复请求属性和规格数据
2. 实现更智能的规格推荐算法
3. 添加规格预览功能
4. 支持批量文件的规格设置
