<view class="container">
  <custom-nav title="我的订单" color="#333" showBack="{{true}}" showRight="{{true}}" rightText="管理" bind:righttap="toggleManage"></custom-nav>
  
  <!-- 订单筛选Tab -->
  <view class="order-tabs">
    <view 
      class="tab-item {{currentTab === index ? 'active' : ''}}" 
      wx:for="{{tabs}}" 
      wx:key="index" 
      bindtap="switchTab" 
      data-index="{{index}}">
      {{item}}
      <view class="tab-line" wx:if="{{currentTab === index}}"></view>
    </view>
  </view>
  
  <!-- 订单列表 -->
  <scroll-view scroll-y="true" class="order-scroll">
    <!-- 普通模式订单列表 -->
    <view class="order-list" wx:if="{{orderList.length > 0 && !manageMode}}">
      <view class="order-item" wx:for="{{orderList}}" wx:key="id" bindtap="viewOrderDetail" data-id="{{item.id}}">
        <view class="order-header">
          <text class="order-id">订单号：{{item.id}}</text>
          <text class="order-status">{{item.statusText}}</text>
        </view>
        
        <view class="order-goods">
          <view class="goods-item" wx:for="{{item.items}}" wx:for-item="goods" wx:key="id">
            <view class="goods-info">
              <view class="goods-name">{{goods.name}}</view>
              <view class="goods-params">{{goods.params}}</view>
              <view class="goods-price">
                <text class="price">¥{{goods.price}}</text>
                <text class="count">x{{goods.quantity}}</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="order-footer">
          <view class="order-total">
            <text>共{{item.items.length}}件</text>
            <text>合计：<text class="total-price">¥{{item.totalPrice}}</text></text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 管理模式订单列表 -->
    <checkbox-group class="order-list" wx:if="{{orderList.length > 0 && manageMode}}" bindchange="onCheckboxGroupChange">
      <view class="order-item" wx:for="{{orderList}}" wx:key="id">
        <view class="checkbox-container">
          <checkbox value="{{item.id}}" checked="{{selectedOrders.indexOf(item.id) !== -1}}" />
        </view>
        <view class="order-content">
          <view class="order-header">
            <text class="order-id">订单号：{{item.id}}</text>
            <text class="order-status">{{item.statusText}}</text>
          </view>
          
          <view class="order-goods">
            <view class="goods-item" wx:for="{{item.items}}" wx:for-item="goods" wx:key="id">
              <view class="goods-info">
                <view class="goods-name">{{goods.name}}</view>
                <view class="goods-params">{{goods.params}}</view>
                <view class="goods-price">
                  <text class="price">¥{{goods.price}}</text>
                  <text class="count">x{{goods.quantity}}</text>
                </view>
              </view>
            </view>
          </view>
          
          <view class="order-footer">
            <view class="order-total">
              <text>共{{item.items.length}}件</text>
              <text>合计：<text class="total-price">¥{{item.totalPrice}}</text></text>
            </view>
          </view>
        </view>
      </view>
    </checkbox-group>
    
    <!-- 无订单提示 -->
    <view class="empty-order" wx:if="{{orderList.length === 0}}">
      <view class="empty-icon">
        <view class="iconfont icon-order-empty"></view>
      </view>
      <text class="empty-text">暂无相关订单</text>
    </view>
    
    <!-- 没有更多了 -->
    <view class="no-more" wx:if="{{!hasMore && orderList.length > 0}}">
      <text>没有更多订单了</text>
    </view>
  </scroll-view>
  
  <!-- 管理模式底部操作 -->
  <view class="manage-bottom" wx:if="{{manageMode}}">
    <button class="manage-btn cancel-selected-btn" bindtap="cancelSelectedOrders">取消订单</button>
    <button class="manage-btn complete-manage-btn" bindtap="toggleManage">完成</button>
  </view>
</view> 