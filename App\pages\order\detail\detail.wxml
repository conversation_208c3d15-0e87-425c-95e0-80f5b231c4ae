<view class="container">
  <custom-nav title="订单详情" color="#333" showBack="{{true}}"></custom-nav>
  
  <!-- 订单状态 -->
  <view class="status-section">
    <view class="status-content">
      <text class="status-text">{{order.statusText}}</text>
      <text class="status-desc" wx:if="{{order.status === 0}}">请尽快完成支付</text>
      <text class="status-desc" wx:elif="{{order.status === 1}}">您的订单即将送达</text>
      <text class="status-desc" wx:elif="{{order.status === 3}}">感谢您的使用</text>
    </view>
  </view>
  
  <!-- 订单信息 -->
  <view class="info-card">
    <view class="card-title">订单信息</view>
    <view class="info-item">
      <text class="info-label">订单编号</text>
      <text class="info-value">{{order.id}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">下单时间</text>
      <text class="info-value">{{order.createTime}}</text>
    </view>
    <view class="info-item">
      <text class="info-label">支付方式</text>
      <text class="info-value">微信支付</text>
    </view>
  </view>
  
  <!-- 配送信息 -->
  <view class="info-card">
    <view class="card-title">配送信息</view>
    <view class="address-info">
      <view class="user-info">
        <text class="user-name">{{order.address.name}}</text>
        <text class="user-phone">{{order.address.phone}}</text>
      </view>
      <view class="address-detail">{{order.address.province}}{{order.address.city}}{{order.address.district}}{{order.address.detailAddress}}</view>
    </view>
  </view>
  
  <!-- 订单内容 -->
  <view class="info-card">
    <view class="card-title">订单内容</view>
    <view class="goods-list">
      <view class="goods-item" wx:for="{{order.items}}" wx:key="id">
        <view class="goods-info">
          <view class="goods-name">{{item.name}}</view>
          <view class="goods-params">{{item.params}}</view>
        </view>
        <view class="goods-price-info">
          <text class="goods-price">¥{{item.price}}</text>
          <text class="goods-count">x{{item.quantity}}</text>
        </view>
      </view>
    </view>
    
    <!-- 价格明细 -->
    <view class="price-detail">
      <view class="price-item">
        <text>商品金额</text>
        <text>¥{{order.totalPrice}}</text>
      </view>
      <view class="price-item">
        <text>配送费</text>
        <text>¥{{order.deliveryFee || '0.00'}}</text>
      </view>
      <view class="price-item discount" wx:if="{{order.discountAmount > 0}}">
        <text>优惠</text>
        <text>-¥{{order.discountAmount}}</text>
      </view>
      <view class="price-item total">
        <text>实付款</text>
        <text class="total-price">¥{{order.payAmount || order.totalPrice}}</text>
      </view>
    </view>
  </view>
  
  <!-- 底部按钮 -->
  <view class="bottom-btns" wx:if="{{order.status === 0}}">
    <button class="btn cancel-btn" bindtap="cancelOrder">取消订单</button>
    <button class="btn pay-btn" bindtap="payOrder">立即支付</button>
  </view>
</view> 