.container {
  padding: 0 0 30rpx 0;
  background-color: #f7f7f7;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 订单筛选Tab */
.order-tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
}

.tab-item.active {
  color: #ff6633;
  font-weight: 500;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 60rpx;
  height: 6rpx;
  background-color: #ff6633;
  border-radius: 3rpx;
  transform: translateX(-50%);
  transition: all 0.3s;
}

/* 滚动区域 */
.order-scroll {
  flex: 1;
  height: calc(100vh - 90rpx - 90rpx); /* 减去顶部导航和筛选Tab的高度 */
}

/* 订单列表 */
.order-list {
  padding: 20rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.order-item:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.order-header {
  display: flex;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.order-id {
  font-size: 26rpx;
  color: #999;
}

.order-status {
  font-size: 26rpx;
  color: #ff6633;
  font-weight: 500;
}

.order-goods {
  padding: 20rpx 30rpx;
}

.goods-item {
  display: flex;
  padding: 15rpx 0;
}

.goods-info {
  flex: 1;
}

.goods-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.goods-params {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.goods-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 28rpx;
  color: #ff6633;
}

.count {
  font-size: 24rpx;
  color: #999;
}

.order-footer {
  padding: 24rpx 30rpx;
  border-top: 1rpx solid #f5f5f5;
}

.order-total {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.order-total text {
  margin-left: 20rpx;
}

.total-price {
  font-size: 30rpx;
  color: #ff6633;
  font-weight: 500;
}

/* 空订单提示 */
.empty-order {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.iconfont.icon-order-empty {
  font-size: 120rpx;
  color: #ddd;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载更多 */
.loading-more, .no-more {
  text-align: center;
  padding: 20rpx 0;
  font-size: 24rpx;
  color: #999;
}

/* 管理模式样式 */
.checkbox-container {
  width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.order-content {
  width: calc(100% - 60rpx);
  display: inline-block;
}
.manage-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
}
.manage-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0 10rpx;
}
.cancel-selected-btn {
  background-color: #f5f5f5;
  color: #666;
}
.complete-manage-btn {
  background: linear-gradient(to right, #ff9933, #ff6633);
  color: #fff;
} 