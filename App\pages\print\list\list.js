var api = require('../../../utils/api.js')
var sender = require('../../../utils/sender.js')
const app = getApp();

Page({
  data: {
    printList: [],
    totalPrice: 0.0,
    selectedCount: 1,
    isAllSelected: false
  },

  onLoad: function(options) {
 
  },
  onShow: function() {
    this.getTempFileList()
  },
  getTempFileList(){
    var that = this
    sender.requestUrl({
      url: api.api_temp_file_list,
      method: 'GET'
    }, function(data) {
      that.setData({
        printList: data
      });
      that.calculateTotal()
    });
  },

  // 判断是否全选
  isAllSelected: function() {
    const printList = this.data.printList;
    if (printList.length === 0) return false;
    return printList.every(item => item.selected);
  },

  // 计算总价和选中数量
  calculateTotal: function() {
    let printList = this.data.printList;
    for(let item of printList){
      let pageNum = item.pageNum
      let pageSize = item.pageSize
      let totalPage = pageSize - pageNum + 1
      let quantity = item.quantity
      let amount = item.unitPrice
      let totalAmount = amount * quantity * totalPage
      item.price = totalAmount
    }

    let totalPrice = 0
    let selectedCount = 0
    
    printList.forEach(item => {
      if (item.selected) {
        totalPrice += item.price
        selectedCount++
      }
    });

    const isAllSelected = printList.length > 0 && printList.every(item => item.selected)

    this.setData({
      totalPrice: totalPrice.toFixed(2),
      selectedCount: selectedCount,
      isAllSelected: isAllSelected,
      printList: printList
    });
  },
  selectAll(){
    let printList = this.data.printList
    for(let item of printList){
      if(item.skuId == null || item.skuId == 0){
        wx.showToast({
          title: '存在未设置打印参数的文件',
          icon: 'none',
          duration: 1000
        })
        return
      }
    }

    for(let item of printList){
      item.selected = !this.data.isAllSelected
    }

    this.setData({
      printList: printList,
      isAllSelected: !this.data.isAllSelected
    })
    this.calculateTotal()
  },
  toggleSelect(e){
    let index = e.currentTarget.dataset.index
    let printList = this.data.printList
    let print = printList[index]
    
    if(print.skuId == null || print.skuId == 0){
      wx.showToast({
        title: '请先设置打印参数',
        icon: 'none',
        duration: 1000
      })
      return
    }
    if(print.selected){
      printList[index].selected = false
    }else{
      printList[index].selected = true
    }
    this.setData({
      printList: printList
    })
    this.calculateTotal()
  },
  
  // 删除文件
  deleteFile: function(e) {
    var that = this
    const id = e.currentTarget.dataset.id
    const printList = this.data.printList
    const newList = printList.filter(item => item.id !== id)

    wx.showModal({
      title: '提示',
      content: '是否确认删除文件',
      success (res) {
        if (res.confirm) {
         
          sender.requestUrl({
            url: api.api_temp_file,
            method: 'DELETE',
            params: {
              id: id
            }
          }, function(data) {
            if(data){
              that.setData({
                printList: newList
              })
              that.calculateTotal()
            }
          })
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      }
    })

    

  
  },

  // 减少数量
  decreaseQuantity: function(e) {
    const index = e.currentTarget.dataset.index;
    const printList = this.data.printList;

    if (printList[index].quantity > 1) {
      printList[index].quantity--;
      this.setData({
        printList: printList
      });
      this.calculateTotal();
    }
  },

  // 增加数量
  increaseQuantity: function(e) {
    const index = e.currentTarget.dataset.index;
    const printList = this.data.printList;

    printList[index].quantity++;
    this.setData({
      printList: printList
    });
    this.calculateTotal();
  },

  // 输入数量变化
  onQuantityInput: function(e) {
    const index = e.currentTarget.dataset.index;
    const value = parseInt(e.detail.value) || 1;
    const printList = this.data.printList;

    if (value >= 1) {
      printList[index].quantity = value;
      this.setData({
        printList: printList
      });
      this.calculateTotal();
    }
  },
  openSettings(e){
    var id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/print/settings/settings?id='+id
    });
  },
  // 继续上传文件
  continueUpload: function() {
    wx.navigateTo({
      url: '/pages/print/upload/upload'
    });
  },

  // 结算
  checkout: function() {
    if (this.data.selectedCount === 0) {
      wx.showToast({
        title: '请先选择要打印的文件',
        icon: 'none'
      });
      return;
    }
   
  }
}) 